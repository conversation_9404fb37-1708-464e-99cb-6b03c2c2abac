import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Filter, 
  X, 
  MapPin, 
  Tag, 
  DollarSign, 
  Calendar,
  SlidersHorizontal,
  RotateCcw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { IVORIAN_CITIES, getCommunesByCity, City, Commune } from '@/data/locations';
import { OBJECT_CATEGORIES, getAllCategories, Category } from '@/data/categories';
import { formatCurrency } from '@/utils/profileValidation';

export interface FilterValues {
  search: string;
  city: string;
  commune: string;
  categories: string[];
  priceRange: [number, number];
  availableFrom: string;
  availableTo: string;
  sortBy: 'price_asc' | 'price_desc' | 'date_desc' | 'date_asc' | 'popularity' | 'distance';
}

interface ExplorerFiltersProps {
  filters: FilterValues;
  onFiltersChange: (filters: FilterValues) => void;
  onClearFilters: () => void;
  className?: string;
}

const ExplorerFilters: React.FC<ExplorerFiltersProps> = ({
  filters,
  onFiltersChange,
  onClearFilters,
  className
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedCity, setSelectedCity] = useState<City | null>(null);
  const [availableCommunes, setAvailableCommunes] = useState<Commune[]>([]);

  // Update available communes when city changes
  useEffect(() => {
    if (filters.city) {
      const city = IVORIAN_CITIES.find(c => c.id === filters.city);
      setSelectedCity(city || null);
      setAvailableCommunes(city ? city.communes : []);
    } else {
      setSelectedCity(null);
      setAvailableCommunes([]);
    }
  }, [filters.city]);

  const updateFilter = (key: keyof FilterValues, value: any) => {
    // Convert "all" values to empty strings for filter logic
    const filterValue = value === 'all' ? '' : value;
    const newFilters = { ...filters, [key]: filterValue };

    // Reset commune when city changes
    if (key === 'city' && filterValue !== filters.city) {
      newFilters.commune = '';
    }

    onFiltersChange(newFilters);
  };

  const toggleCategory = (categoryId: string) => {
    const newCategories = filters.categories.includes(categoryId)
      ? filters.categories.filter(id => id !== categoryId)
      : [...filters.categories, categoryId];
    
    updateFilter('categories', newCategories);
  };

  const hasActiveFilters = () => {
    return (
      filters.search ||
      filters.city ||
      filters.commune ||
      filters.categories.length > 0 ||
      filters.priceRange[0] > 0 ||
      filters.priceRange[1] < 100000 ||
      filters.availableFrom ||
      filters.availableTo
    );
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.city) count++;
    if (filters.commune) count++;
    if (filters.categories.length > 0) count += filters.categories.length;
    if (filters.priceRange[0] > 0 || filters.priceRange[1] < 100000) count++;
    if (filters.availableFrom) count++;
    if (filters.availableTo) count++;
    return count;
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-4 w-4" />
            <span>Filtres</span>
            {hasActiveFilters() && (
              <Badge variant="secondary" className="ml-2">
                {getActiveFiltersCount()}
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            {hasActiveFilters() && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearFilters}
                className="text-xs"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Effacer
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <SlidersHorizontal className="h-3 w-3 mr-1" />
              {isExpanded ? 'Réduire' : 'Étendre'}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search */}
        <div className="space-y-2">
          <Label htmlFor="search" className="text-sm font-medium">
            Recherche
          </Label>
          <Input
            id="search"
            placeholder="Rechercher un objet..."
            value={filters.search}
            onChange={(e) => updateFilter('search', e.target.value)}
          />
        </div>

        {/* Location Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium flex items-center">
              <MapPin className="h-3 w-3 mr-1" />
              Ville
            </Label>
            <Select
              value={filters.city || 'all'}
              onValueChange={(value) => updateFilter('city', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Toutes les villes" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes les villes</SelectItem>
                {IVORIAN_CITIES.map((city) => (
                  <SelectItem key={city.id} value={city.id}>
                    {city.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium">
              Commune
            </Label>
            <Select
              value={filters.commune || 'all'}
              onValueChange={(value) => updateFilter('commune', value)}
              disabled={!filters.city}
            >
              <SelectTrigger>
                <SelectValue placeholder="Toutes les communes" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes les communes</SelectItem>
                {availableCommunes.map((commune) => (
                  <SelectItem key={commune.id} value={commune.id}>
                    {commune.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Categories */}
        <div className="space-y-2">
          <Label className="text-sm font-medium flex items-center">
            <Tag className="h-3 w-3 mr-1" />
            Catégories
          </Label>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {getAllCategories().map((category) => (
              <div key={category.id} className="flex items-center space-x-2">
                <Checkbox
                  id={category.id}
                  checked={filters.categories.includes(category.id)}
                  onCheckedChange={() => toggleCategory(category.id)}
                />
                <Label
                  htmlFor={category.id}
                  className="text-xs cursor-pointer truncate"
                >
                  {category.name}
                </Label>
              </div>
            ))}
          </div>
          
          {filters.categories.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {filters.categories.map((categoryId) => {
                const category = getAllCategories().find(c => c.id === categoryId);
                return category ? (
                  <Badge
                    key={categoryId}
                    variant="secondary"
                    className="text-xs cursor-pointer"
                    onClick={() => toggleCategory(categoryId)}
                  >
                    {category.name}
                    <X className="h-3 w-3 ml-1" />
                  </Badge>
                ) : null;
              })}
            </div>
          )}
        </div>

        {/* Expanded Filters */}
        {isExpanded && (
          <>
            {/* Price Range */}
            <div className="space-y-3">
              <Label className="text-sm font-medium flex items-center">
                <DollarSign className="h-3 w-3 mr-1" />
                Fourchette de prix (par jour)
              </Label>
              
              <div className="px-2">
                <Slider
                  value={filters.priceRange}
                  onValueChange={(value) => updateFilter('priceRange', value as [number, number])}
                  max={100000}
                  min={0}
                  step={1000}
                  className="w-full"
                />
              </div>
              
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>{formatCurrency(filters.priceRange[0])}</span>
                <span>{formatCurrency(filters.priceRange[1])}</span>
              </div>
            </div>

            {/* Availability Dates */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center">
                  <Calendar className="h-3 w-3 mr-1" />
                  Disponible à partir du
                </Label>
                <Input
                  type="date"
                  value={filters.availableFrom}
                  onChange={(e) => updateFilter('availableFrom', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Jusqu'au
                </Label>
                <Input
                  type="date"
                  value={filters.availableTo}
                  onChange={(e) => updateFilter('availableTo', e.target.value)}
                  min={filters.availableFrom || new Date().toISOString().split('T')[0]}
                />
              </div>
            </div>

            {/* Sort Options */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">
                Trier par
              </Label>
              <Select
                value={filters.sortBy}
                onValueChange={(value) => updateFilter('sortBy', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date_desc">Plus récents</SelectItem>
                  <SelectItem value="date_asc">Plus anciens</SelectItem>
                  <SelectItem value="price_asc">Prix croissant</SelectItem>
                  <SelectItem value="price_desc">Prix décroissant</SelectItem>
                  <SelectItem value="popularity">Popularité</SelectItem>
                  <SelectItem value="distance">Distance</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default ExplorerFilters;
