import { useState, useEffect } from 'react';
import { MockService } from '@/data/mockData/mockService';
import { MockObjet, MockFilters } from '@/data/mockData/types';

// Utilisation du type MockObjet
export type Objet = MockObjet;

export const useObjects = (filters?: {
  categorie?: string;
  ville?: string;
  commune?: string;
  prixMin?: number;
  prixMax?: number;
  search?: string;
  dateDebut?: string;
  dateFin?: string;
  sortBy?: string;
}) => {
  const [objects, setObjects] = useState<Objet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchObjects();
  }, [filters]);

  const fetchObjects = async () => {
    try {
      setLoading(true);

      // Convertir les filtres au format MockFilters
      const mockFilters: MockFilters = {
        search: filters?.search,
        // Handle multiple categories by filtering client-side if multiple are selected
        categorie: filters?.categorie && !filters.categorie.includes(',') ? filters.categorie : undefined,
        ville: filters?.ville,
        commune: filters?.commune,
        prix_min: filters?.prixMin,
        prix_max: filters?.prixMax,
        date_debut: filters?.dateDebut,
        date_fin: filters?.dateFin,
        disponible_seulement: true
      };

      // Utiliser le service mock
      const result = await MockService.getObjects(mockFilters);

      // Apply client-side filtering for multiple categories if needed
      let filteredObjects = [...result.objets];
      if (filters?.categorie && filters.categorie.includes(',')) {
        const selectedCategories = filters.categorie.split(',').filter(Boolean);
        filteredObjects = filteredObjects.filter(obj =>
          selectedCategories.includes(obj.categorie_id)
        );
      }

      // Appliquer le tri si spécifié
      let sortedObjects = [...filteredObjects];
      if (filters?.sortBy) {
        switch (filters.sortBy) {
          case 'price_asc':
            sortedObjects.sort((a, b) => a.prix_par_jour - b.prix_par_jour);
            break;
          case 'price_desc':
            sortedObjects.sort((a, b) => b.prix_par_jour - a.prix_par_jour);
            break;
          case 'date_asc':
            sortedObjects.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
            break;
          case 'date_desc':
          default:
            sortedObjects.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
            break;
          case 'popularity':
            sortedObjects.sort((a, b) => (b.vues + b.favoris) - (a.vues + a.favoris));
            break;
        }
      }

      setObjects(sortedObjects);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des objets');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return { objects, loading, error, refetch: fetchObjects };
};