import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { ImageIcon, AlertCircle } from 'lucide-react';

interface SafeImageProps {
  src: string;
  alt: string;
  className?: string;
  fallbackSrc?: string;
  showErrorIcon?: boolean;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
  aspectRatio?: 'square' | 'video' | 'auto';
  objectFit?: 'cover' | 'contain' | 'fill';
}

const SafeImage: React.FC<SafeImageProps> = ({
  src,
  alt,
  className,
  fallbackSrc,
  showErrorIcon = true,
  loading = 'lazy',
  onLoad,
  onError,
  aspectRatio = 'auto',
  objectFit = 'cover'
}) => {
  const [currentSrc, setCurrentSrc] = useState(src);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const maxRetries = 2;

  // Fallback images par catégorie
  const categoryFallbacks = {
    'outils': 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=300&fit=crop',
    'cuisine': 'https://images.unsplash.com/photo-1571175443880-49e1d25b2bc5?w=400&h=300&fit=crop',
    'electronique': 'https://images.unsplash.com/photo-1468495244123-6c6c332eeece?w=400&h=300&fit=crop',
    'mobilier': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
    'vehicules': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop',
    'sport': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
    'default': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop'
  };

  // Détecter la catégorie depuis l'alt ou le src
  const detectCategory = (text: string): string => {
    const lowerText = text.toLowerCase();
    if (lowerText.includes('outil') || lowerText.includes('perceuse') || lowerText.includes('scie')) return 'outils';
    if (lowerText.includes('cuisine') || lowerText.includes('réfrigérateur') || lowerText.includes('micro-onde')) return 'cuisine';
    if (lowerText.includes('électronique') || lowerText.includes('ordinateur') || lowerText.includes('téléphone')) return 'electronique';
    if (lowerText.includes('mobilier') || lowerText.includes('table') || lowerText.includes('chaise')) return 'mobilier';
    if (lowerText.includes('vélo') || lowerText.includes('voiture') || lowerText.includes('moto')) return 'vehicules';
    if (lowerText.includes('sport') || lowerText.includes('fitness') || lowerText.includes('tapis')) return 'sport';
    return 'default';
  };

  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case 'square': return 'aspect-square';
      case 'video': return 'aspect-video';
      default: return '';
    }
  };

  const getObjectFitClass = () => {
    switch (objectFit) {
      case 'contain': return 'object-contain';
      case 'fill': return 'object-fill';
      default: return 'object-cover';
    }
  };

  useEffect(() => {
    setCurrentSrc(src);
    setHasError(false);
    setIsLoading(true);
    setRetryCount(0);
  }, [src]);

  const handleImageLoad = () => {
    setIsLoading(false);
    setHasError(false);
    onLoad?.();
  };

  const handleImageError = () => {
    setIsLoading(false);
    
    if (retryCount < maxRetries) {
      // Retry with cache busting
      const separator = currentSrc.includes('?') ? '&' : '?';
      const newSrc = `${currentSrc}${separator}retry=${retryCount + 1}&t=${Date.now()}`;
      setCurrentSrc(newSrc);
      setRetryCount(prev => prev + 1);
      return;
    }

    // Try fallback image
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc);
      setRetryCount(0);
      return;
    }

    // Try category-specific fallback
    const category = detectCategory(alt);
    const categoryFallback = categoryFallbacks[category];
    if (categoryFallback && currentSrc !== categoryFallback) {
      setCurrentSrc(categoryFallback);
      setRetryCount(0);
      return;
    }

    // Final fallback failed
    setHasError(true);
    onError?.();
  };

  if (hasError) {
    return (
      <div className={cn(
        "flex items-center justify-center bg-muted text-muted-foreground",
        getAspectRatioClass(),
        className
      )}>
        <div className="flex flex-col items-center space-y-2 p-4 text-center">
          {showErrorIcon && <AlertCircle className="h-8 w-8" />}
          <span className="text-sm">Image non disponible</span>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("relative overflow-hidden", getAspectRatioClass(), className)}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted animate-pulse">
          <ImageIcon className="h-8 w-8 text-muted-foreground" />
        </div>
      )}
      
      <img
        src={currentSrc}
        alt={alt}
        loading={loading}
        onLoad={handleImageLoad}
        onError={handleImageError}
        className={cn(
          "w-full h-full transition-opacity duration-300",
          getObjectFitClass(),
          isLoading ? "opacity-0" : "opacity-100"
        )}
      />
    </div>
  );
};

export default SafeImage;
