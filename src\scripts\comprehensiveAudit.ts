// Audit complet de l'application AfroRent Hub

import { MockService } from '@/data/mockData/mockService';

export const runComprehensiveAudit = async () => {
  console.log('🔍 AUDIT COMPLET AFRORENT HUB');
  console.log('=============================');
  console.log('Date:', new Date().toLocaleString('fr-FR'));
  console.log('URL:', window.location.href);
  console.log('');

  const auditResults = {
    images: { total: 0, loaded: 0, failed: 0, issues: [] },
    authentication: { working: false, flows: [], issues: [] },
    navigation: { links: 0, working: 0, broken: [] },
    mockData: { objects: 0, categories: 0, users: 0, functional: false },
    responsive: { mobile: false, tablet: false, desktop: false },
    localization: { french: 0, total: 0, percentage: 0 },
    performance: { loadTime: 0, errors: 0, warnings: 0 },
    accessibility: { score: 0, issues: [] },
    overall: { score: 0, status: 'PENDING' }
  };

  // Test 1: Audit des Images
  console.log('🖼️ 1. AUDIT DES IMAGES');
  console.log('---------------------');
  
  const auditImages = () => {
    const images = document.querySelectorAll('img');
    auditResults.images.total = images.length;
    
    console.log(`   📊 Images trouvées: ${images.length}`);
    
    let loadedCount = 0;
    let failedCount = 0;
    const failedImages = [];

    images.forEach((img, index) => {
      if (img.complete) {
        if (img.naturalWidth > 0) {
          loadedCount++;
        } else {
          failedCount++;
          failedImages.push({
            index: index + 1,
            src: img.src,
            alt: img.alt || 'Sans alt'
          });
        }
      } else {
        // Image en cours de chargement
        img.onload = () => loadedCount++;
        img.onerror = () => {
          failedCount++;
          failedImages.push({
            index: index + 1,
            src: img.src,
            alt: img.alt || 'Sans alt'
          });
        };
      }
    });

    auditResults.images.loaded = loadedCount;
    auditResults.images.failed = failedCount;
    auditResults.images.issues = failedImages;

    console.log(`   ✅ Images chargées: ${loadedCount}`);
    console.log(`   ❌ Images échouées: ${failedCount}`);
    
    if (failedImages.length > 0) {
      console.log('   🔍 Images problématiques:');
      failedImages.forEach(img => {
        console.log(`     ${img.index}. ${img.alt} - ${img.src.substring(0, 50)}...`);
      });
    }

    // Test des images Unsplash spécifiquement
    const unsplashImages = Array.from(images).filter(img => 
      img.src.includes('unsplash.com')
    );
    console.log(`   🌐 Images Unsplash: ${unsplashImages.length}`);

    // Test des avatars
    const avatars = document.querySelectorAll('[class*="avatar"], [class*="Avatar"]');
    console.log(`   👤 Avatars détectés: ${avatars.length}`);
  };

  auditImages();

  // Test 2: Audit de l'Authentification
  console.log('\n🔐 2. AUDIT DE L\'AUTHENTIFICATION');
  console.log('----------------------------------');
  
  const auditAuthentication = async () => {
    const flows = [];
    
    try {
      // Test de connexion
      console.log('   🧪 Test de connexion...');
      const loginResult = await MockService.signIn('<EMAIL>', 'password123');
      
      if (loginResult.user && !loginResult.error) {
        flows.push({ name: 'Connexion', status: 'SUCCESS' });
        console.log('   ✅ Connexion: Réussie');
        
        // Test de récupération du profil
        const profile = await MockService.getUserById(loginResult.user.id);
        if (profile) {
          flows.push({ name: 'Récupération profil', status: 'SUCCESS' });
          console.log('   ✅ Profil: Récupéré');
        } else {
          flows.push({ name: 'Récupération profil', status: 'FAILED' });
          console.log('   ❌ Profil: Échec');
        }
        
        auditResults.authentication.working = true;
      } else {
        flows.push({ name: 'Connexion', status: 'FAILED' });
        console.log('   ❌ Connexion: Échec -', loginResult.error);
        auditResults.authentication.issues.push(loginResult.error);
      }

      // Test de connexion avec mauvais identifiants
      const badLogin = await MockService.signIn('<EMAIL>', 'wrongpass');
      if (badLogin.error) {
        flows.push({ name: 'Rejet mauvais identifiants', status: 'SUCCESS' });
        console.log('   ✅ Sécurité: Mauvais identifiants rejetés');
      } else {
        flows.push({ name: 'Rejet mauvais identifiants', status: 'FAILED' });
        console.log('   ❌ Sécurité: Mauvais identifiants acceptés');
      }

      // Test des routes protégées
      const protectedRoutes = ['/dashboard', '/admin', '/profile/edit'];
      console.log('   🛡️ Routes protégées:', protectedRoutes.join(', '));

      auditResults.authentication.flows = flows;
      
    } catch (error) {
      console.log('   ❌ Erreur lors du test d\'authentification:', error.message);
      auditResults.authentication.issues.push(error.message);
    }
  };

  await auditAuthentication();

  // Test 3: Audit de la Navigation
  console.log('\n🧭 3. AUDIT DE LA NAVIGATION');
  console.log('----------------------------');
  
  const auditNavigation = () => {
    const links = document.querySelectorAll('a[href]');
    auditResults.navigation.links = links.length;
    
    console.log(`   🔗 Liens trouvés: ${links.length}`);
    
    const brokenLinks = [];
    let workingLinks = 0;

    links.forEach((link, index) => {
      const href = link.getAttribute('href');
      
      // Vérifier les liens internes
      if (href?.startsWith('/') || href?.startsWith('#')) {
        // Lien interne - vérifier qu'il n'est pas vide
        if (href.length > 1) {
          workingLinks++;
        } else {
          brokenLinks.push({
            index: index + 1,
            href,
            text: link.textContent?.trim() || 'Sans texte'
          });
        }
      } else if (href?.startsWith('http')) {
        // Lien externe - considérer comme fonctionnel
        workingLinks++;
      } else if (href) {
        // Lien suspect
        brokenLinks.push({
          index: index + 1,
          href,
          text: link.textContent?.trim() || 'Sans texte'
        });
      }
    });

    auditResults.navigation.working = workingLinks;
    auditResults.navigation.broken = brokenLinks;

    console.log(`   ✅ Liens fonctionnels: ${workingLinks}`);
    console.log(`   ❌ Liens suspects: ${brokenLinks.length}`);

    if (brokenLinks.length > 0) {
      console.log('   🔍 Liens problématiques:');
      brokenLinks.slice(0, 5).forEach(link => {
        console.log(`     ${link.index}. "${link.text}" → ${link.href}`);
      });
    }

    // Test de la navigation mobile
    const mobileNav = document.querySelector('nav[class*="bottom"]');
    console.log(`   📱 Navigation mobile: ${mobileNav ? 'Présente' : 'Absente'}`);
  };

  auditNavigation();

  // Test 4: Audit des Données Mock
  console.log('\n📊 4. AUDIT DES DONNÉES MOCK');
  console.log('----------------------------');
  
  const auditMockData = async () => {
    try {
      // Test des objets
      const objectsResult = await MockService.getObjects();
      auditResults.mockData.objects = objectsResult.objets.length;
      console.log(`   📦 Objets: ${objectsResult.objets.length}`);

      // Test des catégories
      const categories = await MockService.getCategories();
      auditResults.mockData.categories = categories.length;
      console.log(`   📂 Catégories: ${categories.length}`);

      // Test des utilisateurs
      const users = await MockService.getUsers();
      auditResults.mockData.users = users.length;
      console.log(`   👥 Utilisateurs: ${users.length}`);

      // Test de filtrage
      const filteredResult = await MockService.getObjects({ ville: 'Abidjan' });
      console.log(`   🔍 Filtrage (Abidjan): ${filteredResult.objets.length} objets`);

      auditResults.mockData.functional = 
        objectsResult.objets.length > 0 && 
        categories.length > 0 && 
        users.length > 0;

      console.log(`   ✅ Système mock: ${auditResults.mockData.functional ? 'Fonctionnel' : 'Défaillant'}`);

    } catch (error) {
      console.log('   ❌ Erreur données mock:', error.message);
      auditResults.mockData.functional = false;
    }
  };

  await auditMockData();

  // Test 5: Audit Responsive
  console.log('\n📱 5. AUDIT RESPONSIVE');
  console.log('---------------------');
  
  const auditResponsive = () => {
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    console.log(`   📐 Viewport: ${viewport.width}x${viewport.height}`);

    auditResults.responsive.mobile = viewport.width < 768;
    auditResults.responsive.tablet = viewport.width >= 768 && viewport.width < 1024;
    auditResults.responsive.desktop = viewport.width >= 1024;

    console.log(`   📱 Mobile: ${auditResults.responsive.mobile ? 'Oui' : 'Non'}`);
    console.log(`   📟 Tablet: ${auditResults.responsive.tablet ? 'Oui' : 'Non'}`);
    console.log(`   🖥️ Desktop: ${auditResults.responsive.desktop ? 'Oui' : 'Non'}`);

    // Test des classes responsive
    const responsiveElements = document.querySelectorAll('[class*="sm:"], [class*="md:"], [class*="lg:"]');
    console.log(`   🎨 Éléments responsive: ${responsiveElements.length}`);

    // Test du layout mobile
    const mobileLayout = document.querySelector('[class*="mobile"]');
    console.log(`   📱 Layout mobile: ${mobileLayout ? 'Détecté' : 'Non détecté'}`);
  };

  auditResponsive();

  // Test 6: Audit Localisation
  console.log('\n🇫🇷 6. AUDIT LOCALISATION');
  console.log('-------------------------');
  
  const auditLocalization = () => {
    const frenchTexts = [
      'Accueil', 'Explorer', 'Tableau de bord', 'Connexion', 'Inscription',
      'Réserver', 'Contacter', 'Propriétaire', 'Disponible', 'par jour',
      'FCFA', 'Catégories', 'Rechercher', 'Filtrer', 'Trier'
    ];

    let foundTexts = 0;
    const bodyText = document.body.textContent || '';

    frenchTexts.forEach(text => {
      if (bodyText.includes(text)) {
        foundTexts++;
      }
    });

    auditResults.localization.french = foundTexts;
    auditResults.localization.total = frenchTexts.length;
    auditResults.localization.percentage = Math.round((foundTexts / frenchTexts.length) * 100);

    console.log(`   🇫🇷 Textes français: ${foundTexts}/${frenchTexts.length} (${auditResults.localization.percentage}%)`);

    if (auditResults.localization.percentage < 80) {
      console.log('   ⚠️ Localisation incomplète');
    } else {
      console.log('   ✅ Localisation satisfaisante');
    }
  };

  auditLocalization();

  // Calcul du score global
  setTimeout(() => {
    console.log('\n🎯 RAPPORT FINAL D\'AUDIT');
    console.log('========================');

    // Calcul des scores
    const imageScore = auditResults.images.total > 0 ? 
      (auditResults.images.loaded / auditResults.images.total) * 100 : 0;
    
    const authScore = auditResults.authentication.working ? 100 : 0;
    
    const navScore = auditResults.navigation.links > 0 ? 
      (auditResults.navigation.working / auditResults.navigation.links) * 100 : 0;
    
    const dataScore = auditResults.mockData.functional ? 100 : 0;
    
    const localizationScore = auditResults.localization.percentage;

    const overallScore = Math.round(
      (imageScore + authScore + navScore + dataScore + localizationScore) / 5
    );

    auditResults.overall.score = overallScore;
    
    if (overallScore >= 90) auditResults.overall.status = 'EXCELLENT';
    else if (overallScore >= 75) auditResults.overall.status = 'BON';
    else if (overallScore >= 60) auditResults.overall.status = 'ACCEPTABLE';
    else auditResults.overall.status = 'CRITIQUE';

    console.log(`🏆 Score global: ${overallScore}% (${auditResults.overall.status})`);
    console.log('');
    console.log('📊 Détail des scores:');
    console.log(`   🖼️ Images: ${Math.round(imageScore)}%`);
    console.log(`   🔐 Authentification: ${authScore}%`);
    console.log(`   🧭 Navigation: ${Math.round(navScore)}%`);
    console.log(`   📊 Données: ${dataScore}%`);
    console.log(`   🇫🇷 Localisation: ${localizationScore}%`);
    console.log('');

    // Recommandations
    console.log('📋 RECOMMANDATIONS:');
    if (imageScore < 90) {
      console.log('   🖼️ Améliorer la gestion des images');
    }
    if (authScore < 100) {
      console.log('   🔐 Corriger l\'authentification');
    }
    if (navScore < 90) {
      console.log('   🧭 Vérifier les liens de navigation');
    }
    if (dataScore < 100) {
      console.log('   📊 Corriger le système de données');
    }
    if (localizationScore < 80) {
      console.log('   🇫🇷 Compléter la localisation française');
    }

    if (overallScore >= 90) {
      console.log('🎉 APPLICATION PRÊTE POUR LA PRODUCTION !');
    } else {
      console.log('🔧 Corrections nécessaires avant production');
    }

    return auditResults;
  }, 2000);

  return auditResults;
};

export default runComprehensiveAudit;
