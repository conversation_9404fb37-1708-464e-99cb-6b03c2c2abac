import { MockObjet } from './types';
import { MOCK_USERS } from './users';

// Images de haute qualité pour chaque catégorie avec paramètres optimisés
const CATEGORY_IMAGES = {
  'outils-electriques': [
    'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1572981779307-38b8cabb2407?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1504148455328-c376907d081c?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?w=600&h=400&fit=crop&auto=format&q=80'
  ],
  'outils-manuels': [
    'https://images.unsplash.com/photo-1530124566582-a618bc2615dc?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=600&h=400&fit=crop&auto=format&q=80'
  ],
  'cuisine': [
    'https://images.unsplash.com/photo-1571175443880-49e1d25b2bc5?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1574269909862-7e1d70bb8078?w=600&h=400&fit=crop&auto=format&q=80'
  ],
  'lavage': [
    'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1574269909862-7e1d70bb8078?w=600&h=400&fit=crop&auto=format&q=80'
  ],
  'photo-video': [
    'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1516035069371-29a1b244cc32?w=600&h=400&fit=crop&auto=format&q=80'
  ],
  'informatique': [
    'https://images.unsplash.com/photo-1468495244123-6c6c332eeece?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1484788984921-03950022c9ef?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=600&h=400&fit=crop&auto=format&q=80'
  ],
  'audio': [
    'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1558756520-22cfe5d382ca?w=600&h=400&fit=crop&auto=format&q=80'
  ],
  'velos': [
    'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1571068316344-75bc76f77890?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1544191696-15693072b5a5?w=600&h=400&fit=crop&auto=format&q=80'
  ],
  'motos': [
    'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1449426468159-d96dbf08f19f?w=600&h=400&fit=crop&auto=format&q=80'
  ],
  'voitures': [
    'https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=600&h=400&fit=crop&auto=format&q=80'
  ],
  'fitness': [
    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=600&h=400&fit=crop&auto=format&q=80'
  ],
  'mobilier': [
    'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1506439773649-6e0eb8cfb237?w=600&h=400&fit=crop&auto=format&q=80'
  ],
  'evenementiel': [
    'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?w=600&h=400&fit=crop&auto=format&q=80'
  ],
  'vetements-mode': [
    'https://images.unsplash.com/photo-1566479179817-c0b5b4b4b1e5?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=600&h=400&fit=crop&auto=format&q=80'
  ],
  'jardinage': [
    'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=600&h=400&fit=crop&auto=format&q=80',
    'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=600&h=400&fit=crop&auto=format&q=80'
  ]
};

// Villes et communes de Côte d'Ivoire
const LOCATIONS = {
  'Abidjan': ['Cocody', 'Plateau', 'Marcory', 'Treichville', 'Adjamé', 'Yopougon', 'Abobo', 'Koumassi'],
  'Bouaké': ['Koko', 'Dar-Es-Salam', 'Kennedy', 'Belleville'],
  'Daloa': ['Centre', 'Tazibouo', 'Lobia'],
  'Yamoussoukro': ['Centre', 'Habitat', 'Millionnaire'],
  'San-Pédro': ['Centre', 'Bardot', 'Balmer'],
  'Korhogo': ['Centre', 'Petit-Paris', 'Résidentiel'],
  'Man': ['Centre', 'Libreville'],
  'Divo': ['Centre', 'Hiré'],
  'Gagnoa': ['Centre', 'Dignago'],
  'Abengourou': ['Centre', 'Aniassué']
};

// Templates d'objets par catégorie
const OBJECT_TEMPLATES = {
  'outils-electriques': [
    {
      titre: 'Perceuse professionnelle Bosch',
      description: 'Perceuse sans fil professionnelle 18V avec batterie longue durée. Parfaite pour percer le bois, le métal et la maçonnerie. Livrée avec coffret d\'accessoires complet.',
      prix_base: 8000,
      caution_base: 25000,
      tags: ['bricolage', 'professionnel', 'sans-fil'],
      conditions: 'Manipulation avec précaution. Retour dans l\'état initial. Batterie chargée.'
    },
    {
      titre: 'Scie circulaire Makita',
      description: 'Scie circulaire électrique 1200W pour coupes précises. Idéale pour le bois et les matériaux composites. Guide de coupe inclus.',
      prix_base: 6000,
      caution_base: 20000,
      tags: ['coupe', 'bois', 'précision'],
      conditions: 'Utilisation par personne expérimentée uniquement. Équipements de protection obligatoires.'
    },
    {
      titre: 'Ponceuse orbitale Festool',
      description: 'Ponceuse orbitale professionnelle avec aspiration intégrée. Parfaite pour les finitions de qualité. Disques abrasifs fournis.',
      prix_base: 7000,
      caution_base: 22000,
      tags: ['ponçage', 'finition', 'aspiration'],
      conditions: 'Nettoyage après usage. Vider le bac à poussière.'
    }
  ],
  'cuisine': [
    {
      titre: 'Réfrigérateur Samsung 300L',
      description: 'Réfrigérateur moderne avec congélateur, classe énergétique A+. Idéal pour événements ou dépannage temporaire. Très silencieux.',
      prix_base: 12000,
      caution_base: 80000,
      tags: ['froid', 'économique', 'silencieux'],
      conditions: 'Nettoyage complet avant retour. Dégivrage si nécessaire.'
    },
    {
      titre: 'Four micro-ondes LG 25L',
      description: 'Micro-ondes multifonction avec grill et décongélation automatique. Parfait pour réchauffer et cuire rapidement.',
      prix_base: 4000,
      caution_base: 15000,
      tags: ['cuisson', 'rapide', 'multifonction'],
      conditions: 'Nettoyage intérieur obligatoire. Plateau tournant à manipuler avec soin.'
    }
  ],
  'photo-video': [
    {
      titre: 'Appareil photo Canon EOS R6',
      description: 'Appareil photo hybride professionnel 24MP avec stabilisation intégrée. Objectif 24-70mm f/2.8 inclus. Parfait pour événements et portraits.',
      prix_base: 25000,
      caution_base: 150000,
      tags: ['professionnel', 'hybride', 'stabilisé'],
      conditions: 'Manipulation délicate. Retour avec batterie chargée. Carte mémoire fournie.'
    },
    {
      titre: 'Caméra Sony FX3',
      description: 'Caméra cinéma compacte 4K avec autofocus avancé. Idéale pour tournages professionnels et créations de contenu.',
      prix_base: 35000,
      caution_base: 200000,
      tags: ['cinéma', '4K', 'autofocus'],
      conditions: 'Expérience en vidéo requise. Accessoires à retourner complets.'
    }
  ],
  'velos': [
    {
      titre: 'Vélo électrique urbain Decathlon',
      description: 'Vélo électrique confortable pour la ville, autonomie 50km. Batterie amovible, éclairage LED intégré. Parfait pour les déplacements quotidiens.',
      prix_base: 15000,
      caution_base: 120000,
      tags: ['électrique', 'urbain', 'autonomie'],
      conditions: 'Retour avec batterie chargée. Vérification des freins avant usage.'
    },
    {
      titre: 'VTT Trek tout-terrain',
      description: 'VTT robuste pour sentiers et chemins difficiles. Suspension avant, 21 vitesses. Idéal pour les aventures en nature.',
      prix_base: 10000,
      caution_base: 60000,
      tags: ['VTT', 'tout-terrain', 'suspension'],
      conditions: 'Nettoyage après usage en extérieur. Vérification de la pression des pneus.'
    }
  ],
  'fitness': [
    {
      titre: 'Tapis de course NordicTrack',
      description: 'Tapis de course électrique avec programmes d\'entraînement intégrés. Écran LCD, pliable pour gain de place.',
      prix_base: 18000,
      caution_base: 100000,
      tags: ['cardio', 'programmes', 'pliable'],
      conditions: 'Installation et démontage inclus. Surface plane requise.'
    },
    {
      titre: 'Set haltères ajustables',
      description: 'Set complet d\'haltères ajustables de 5 à 25kg chacun. Support inclus. Parfait pour musculation à domicile.',
      prix_base: 8000,
      caution_base: 40000,
      tags: ['musculation', 'ajustable', 'complet'],
      conditions: 'Comptage des poids au retour. Manipulation soigneuse.'
    }
  ]
};

// Fonction pour générer des objets aléatoirement
const generateRandomObjects = (): MockObjet[] => {
  const objects: MockObjet[] = [];
  let objectId = 1;

  // Générer des objets pour chaque catégorie
  Object.entries(OBJECT_TEMPLATES).forEach(([categoryId, templates]) => {
    templates.forEach((template, templateIndex) => {
      // Générer plusieurs variations de chaque template
      for (let variation = 0; variation < 3; variation++) {
        const villes = Object.keys(LOCATIONS);
        const ville = villes[Math.floor(Math.random() * villes.length)];
        const communes = LOCATIONS[ville];
        const commune = communes[Math.floor(Math.random() * communes.length)];
        
        // Sélectionner un propriétaire aléatoire (loueur)
        const loueurs = MOCK_USERS.filter(user => user.role === 'loueur');
        const proprietaire = loueurs[Math.floor(Math.random() * loueurs.length)];
        
        // Variation des prix
        const prixVariation = 1 + (Math.random() - 0.5) * 0.4; // ±20%
        const prix = Math.round(template.prix_base * prixVariation / 500) * 500; // Arrondi à 500
        const caution = Math.round(template.caution_base * prixVariation / 1000) * 1000; // Arrondi à 1000
        
        // Sélection d'images
        const categoryImages = CATEGORY_IMAGES[categoryId] || CATEGORY_IMAGES['outils-electriques'];
        const numImages = Math.floor(Math.random() * 3) + 2; // 2-4 images
        const selectedImages = categoryImages
          .sort(() => Math.random() - 0.5)
          .slice(0, numImages);
        
        // Génération des dates
        const createdDate = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000); // 0-90 jours
        const updatedDate = new Date(createdDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000);
        
        // Variation du titre pour les variations
        let titre = template.titre;
        if (variation === 1) titre += ' - Modèle récent';
        if (variation === 2) titre += ' - État excellent';
        
        const objet: MockObjet = {
          id: `objet-${objectId}`,
          titre,
          description: template.description,
          prix_par_jour: prix,
          caution,
          proprietaire_id: proprietaire.id,
          categorie_id: categoryId,
          localisation_ville: ville,
          localisation_commune: commune,
          images: selectedImages,
          disponible: Math.random() > 0.15, // 85% disponibles
          conditions: template.conditions,
          created_at: createdDate.toISOString(),
          updated_at: updatedDate.toISOString(),
          vues: Math.floor(Math.random() * 200) + 10,
          favoris: Math.floor(Math.random() * 50),
          note_moyenne: Math.round((4.0 + Math.random() * 1.0) * 10) / 10,
          nombre_avis: Math.floor(Math.random() * 20) + 1,
          tags: template.tags,
          etat: ['excellent', 'tres_bon', 'bon'][Math.floor(Math.random() * 3)] as any,
          livraison_possible: Math.random() > 0.4, // 60% avec livraison
          prix_livraison: Math.random() > 0.4 ? Math.floor(Math.random() * 3000) + 1000 : undefined,
          duree_min_location: Math.floor(Math.random() * 3) + 1, // 1-3 jours
          duree_max_location: Math.floor(Math.random() * 25) + 5 // 5-30 jours
        };
        
        objects.push(objet);
        objectId++;
      }
    });
  });

  return objects;
};

// Génération des objets mock
export const MOCK_OBJECTS: MockObjet[] = generateRandomObjects();

// Fonctions utilitaires
export const getObjectById = (id: string): MockObjet | undefined => {
  return MOCK_OBJECTS.find(obj => obj.id === id);
};

export const getObjectsByCategory = (categoryId: string): MockObjet[] => {
  return MOCK_OBJECTS.filter(obj => obj.categorie_id === categoryId);
};

export const getObjectsByVille = (ville: string): MockObjet[] => {
  return MOCK_OBJECTS.filter(obj => obj.localisation_ville === ville);
};

export const getObjectsByProprietaire = (proprietaireId: string): MockObjet[] => {
  return MOCK_OBJECTS.filter(obj => obj.proprietaire_id === proprietaireId);
};

export const getAvailableObjects = (): MockObjet[] => {
  return MOCK_OBJECTS.filter(obj => obj.disponible);
};

export const getFeaturedObjects = (limit: number = 6): MockObjet[] => {
  return MOCK_OBJECTS
    .filter(obj => obj.disponible)
    .sort((a, b) => (b.vues + b.favoris) - (a.vues + a.favoris))
    .slice(0, limit);
};

export const searchObjects = (query: string): MockObjet[] => {
  const searchTerm = query.toLowerCase();
  return MOCK_OBJECTS.filter(obj => 
    obj.titre.toLowerCase().includes(searchTerm) ||
    obj.description.toLowerCase().includes(searchTerm) ||
    obj.tags.some(tag => tag.toLowerCase().includes(searchTerm))
  );
};

export default MOCK_OBJECTS;
