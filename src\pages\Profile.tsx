import React, { useState } from 'react';
import { useAuth } from '@/contexts/MockAuthContext';
import { Navigate, Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import AvatarUpload from '@/components/AvatarUpload';
import { Edit, User, Mail, Phone, MapPin, Settings, LogOut, Shield, Bell } from 'lucide-react';
import { cn } from '@/lib/utils';

const Profile = () => {
  const { user, profile, loading, signOut } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-muted rounded w-1/2 mb-4" />
            <div className="h-32 bg-muted rounded" />
            <div className="space-y-3">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-16 bg-muted rounded" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 z-30 bg-background/95 backdrop-blur-lg border-b border-border/40 px-4 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold">Mon Profil</h1>
          <Link to="/profile/edit">
            <Button variant="outline" size="sm">
              <Edit className="h-4 w-4 mr-2" />
              Modifier
            </Button>
          </Link>
        </div>
      </div>

      <div className="px-4 py-6 space-y-6">
        {/* Profile Header */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col items-center space-y-4 text-center">
              <AvatarUpload size="xl" showUploadButton={true} />
              <div>
                <h2 className="text-xl font-bold">
                  {profile?.prenom} {profile?.nom}
                </h2>
                <p className="text-muted-foreground">{profile?.email}</p>
                <Badge variant="outline" className="mt-2">
                  {profile?.role === 'loueur' ? 'Loueur' : 'Locataire'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Informations personnelles</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between py-3 border-b border-border/50">
                <div className="flex items-center space-x-3">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Nom complet</span>
                </div>
                <span className="text-sm text-muted-foreground">
                  {profile?.prenom} {profile?.nom}
                </span>
              </div>
              
              <div className="flex items-center justify-between py-3 border-b border-border/50">
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Email</span>
                </div>
                <span className="text-sm text-muted-foreground">{profile?.email}</span>
              </div>
              
              <div className="flex items-center justify-between py-3 border-b border-border/50">
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Téléphone</span>
                </div>
                <span className="text-sm text-muted-foreground">
                  {profile?.telephone || 'Non renseigné'}
                </span>
              </div>
              
              <div className="flex items-center justify-between py-3 border-b border-border/50">
                <div className="flex items-center space-x-3">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Localisation</span>
                </div>
                <span className="text-sm text-muted-foreground">
                  {profile?.commune && profile?.ville 
                    ? `${profile.commune}, ${profile.ville}`
                    : profile?.ville || 'Non renseigné'
                  }
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Account Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Paramètres du compte</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Link to="/profile/edit" className="block">
              <Button variant="outline" className="w-full justify-start">
                <Edit className="h-4 w-4 mr-3" />
                Modifier mes informations
              </Button>
            </Link>
            
            <Button variant="outline" className="w-full justify-start" disabled>
              <Bell className="h-4 w-4 mr-3" />
              Notifications
              <Badge variant="secondary" className="ml-auto">Bientôt</Badge>
            </Button>
            
            <Button variant="outline" className="w-full justify-start" disabled>
              <Shield className="h-4 w-4 mr-3" />
              Sécurité et confidentialité
              <Badge variant="secondary" className="ml-auto">Bientôt</Badge>
            </Button>
          </CardContent>
        </Card>

        {/* Account Actions */}
        <Card>
          <CardContent className="p-6">
            <Button 
              variant="outline" 
              className="w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10"
              onClick={handleSignOut}
            >
              <LogOut className="h-4 w-4 mr-3" />
              Se déconnecter
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Profile;
