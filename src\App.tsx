import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/MockAuthContext";
import MobileLayout from "@/components/MobileLayout";
import AdminLayout from "@/components/AdminLayout";
import ProtectedRoute from "@/components/ProtectedRoute";
import Index from "./pages/Index";
import Auth from "./pages/Auth";
import Explorer from "./pages/Explorer";
import Dashboard from "./pages/Dashboard";
import Profile from "./pages/Profile";
import ObjetDetail from "./pages/ObjetDetail";
import ProfileEdit from "./components/ProfileEdit";
import AdminDashboard from "./pages/admin/AdminDashboard";
import TestComponents from "./pages/TestComponents";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={
              <MobileLayout showNotifications={true}>
                <Index />
              </MobileLayout>
            } />
            <Route path="/auth" element={
              <ProtectedRoute requireAuth={false}>
                <MobileLayout showHeader={false} showBottomNav={false}>
                  <Auth />
                </MobileLayout>
              </ProtectedRoute>
            } />
            <Route path="/explorer" element={
              <MobileLayout showSearch={true}>
                <Explorer />
              </MobileLayout>
            } />
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <MobileLayout showNotifications={true}>
                  <Dashboard />
                </MobileLayout>
              </ProtectedRoute>
            } />
            <Route path="/objet/:id" element={
              <MobileLayout>
                <ObjetDetail />
              </MobileLayout>
            } />
            <Route path="/profile" element={
              <ProtectedRoute>
                <MobileLayout showNotifications={true}>
                  <Profile />
                </MobileLayout>
              </ProtectedRoute>
            } />
            <Route path="/profile/edit" element={
              <ProtectedRoute>
                <MobileLayout headerTitle="Modifier le profil">
                  <ProfileEdit />
                </MobileLayout>
              </ProtectedRoute>
            } />
            {/* Admin Routes */}
            <Route path="/admin" element={
              <ProtectedRoute>
                <AdminLayout title="Tableau de bord">
                  <AdminDashboard />
                </AdminLayout>
              </ProtectedRoute>
            } />
            <Route path="/admin/users" element={
              <ProtectedRoute>
                <AdminLayout title="Gestion des utilisateurs">
                  <div>Users Management - Coming Soon</div>
                </AdminLayout>
              </ProtectedRoute>
            } />
            <Route path="/admin/objects" element={
              <ProtectedRoute>
                <AdminLayout title="Gestion des objets">
                  <div>Objects Management - Coming Soon</div>
                </AdminLayout>
              </ProtectedRoute>
            } />
            <Route path="/admin/reservations" element={
              <ProtectedRoute>
                <AdminLayout title="Gestion des réservations">
                  <div>Reservations Management - Coming Soon</div>
                </AdminLayout>
              </ProtectedRoute>
            } />
            <Route path="/admin/moderation" element={
              <ProtectedRoute>
                <AdminLayout title="Modération de contenu">
                  <div>Content Moderation - Coming Soon</div>
                </AdminLayout>
              </ProtectedRoute>
            } />
            <Route path="/admin/analytics" element={
              <ProtectedRoute>
                <AdminLayout title="Analyses et rapports">
                  <div>Analytics & Reports - Coming Soon</div>
                </AdminLayout>
              </ProtectedRoute>
            } />
            <Route path="/admin/settings" element={
              <ProtectedRoute>
                <AdminLayout title="Configuration système">
                  <div>System Settings - Coming Soon</div>
                </AdminLayout>
              </ProtectedRoute>
            } />
            {/* Test Route */}
            <Route path="/test" element={
              <MobileLayout headerTitle="Test des Composants">
                <TestComponents />
              </MobileLayout>
            } />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={
              <MobileLayout>
                <NotFound />
              </MobileLayout>
            } />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
