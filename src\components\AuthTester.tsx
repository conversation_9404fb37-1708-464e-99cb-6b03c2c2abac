import React, { useState } from 'react';
import { useAuth } from '@/contexts/MockAuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, User, Mail, Shield, Clock } from 'lucide-react';

const AuthTester: React.FC = () => {
  const { user, profile, loading, signIn, signOut } = useAuth();
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [testPassword, setTestPassword] = useState('password123');
  const [testResult, setTestResult] = useState<string | null>(null);
  const [isTestingAuth, setIsTestingAuth] = useState(false);

  const handleTestLogin = async () => {
    setIsTestingAuth(true);
    setTestResult(null);
    
    try {
      const result = await signIn(testEmail, testPassword);
      
      if (result.error) {
        setTestResult(`❌ Erreur: ${result.error}`);
      } else {
        setTestResult('✅ Connexion réussie !');
      }
    } catch (error) {
      setTestResult(`❌ Erreur inattendue: ${error.message}`);
    } finally {
      setIsTestingAuth(false);
    }
  };

  const handleTestLogout = async () => {
    try {
      await signOut();
      setTestResult('✅ Déconnexion réussie !');
    } catch (error) {
      setTestResult(`❌ Erreur déconnexion: ${error.message}`);
    }
  };

  const getStatusBadge = (condition: boolean, trueText: string, falseText: string) => {
    return (
      <Badge variant={condition ? "default" : "destructive"} className="ml-2">
        {condition ? (
          <>
            <CheckCircle className="w-3 h-3 mr-1" />
            {trueText}
          </>
        ) : (
          <>
            <XCircle className="w-3 h-3 mr-1" />
            {falseText}
          </>
        )}
      </Badge>
    );
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="w-5 h-5 mr-2" />
          Testeur d'Authentification
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* État actuel */}
        <div className="space-y-3">
          <h3 className="font-semibold">État Actuel</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-2" />
              Chargement
              {getStatusBadge(!loading, 'Terminé', 'En cours')}
            </div>
            <div className="flex items-center">
              <User className="w-4 h-4 mr-2" />
              Utilisateur
              {getStatusBadge(!!user, 'Connecté', 'Déconnecté')}
            </div>
            <div className="flex items-center">
              <Mail className="w-4 h-4 mr-2" />
              Profil
              {getStatusBadge(!!profile, 'Chargé', 'Non chargé')}
            </div>
            <div className="flex items-center">
              <Shield className="w-4 h-4 mr-2" />
              Session
              {getStatusBadge(!!localStorage.getItem('afrorent_user'), 'Persistée', 'Absente')}
            </div>
          </div>
        </div>

        {/* Informations utilisateur */}
        {user && profile && (
          <div className="space-y-3">
            <h3 className="font-semibold">Informations Utilisateur</h3>
            <div className="bg-muted p-4 rounded-lg space-y-2 text-sm">
              <div><strong>Email:</strong> {profile.email}</div>
              <div><strong>Nom:</strong> {profile.prenom} {profile.nom}</div>
              <div><strong>Rôle:</strong> {profile.role}</div>
              <div><strong>Ville:</strong> {profile.ville}</div>
              <div><strong>Vérifié:</strong> {profile.verified ? 'Oui' : 'Non'}</div>
              <div><strong>Note:</strong> {profile.note_moyenne}/5.0 ({profile.nombre_avis} avis)</div>
            </div>
          </div>
        )}

        {/* Test de connexion */}
        {!user && (
          <div className="space-y-3">
            <h3 className="font-semibold">Test de Connexion</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="test-email">Email de test</Label>
                <Input
                  id="test-email"
                  type="email"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="test-password">Mot de passe</Label>
                <Input
                  id="test-password"
                  type="password"
                  value={testPassword}
                  onChange={(e) => setTestPassword(e.target.value)}
                  placeholder="password123"
                />
              </div>
            </div>
            <Button 
              onClick={handleTestLogin} 
              disabled={isTestingAuth}
              className="w-full"
            >
              {isTestingAuth ? 'Test en cours...' : 'Tester la Connexion'}
            </Button>
          </div>
        )}

        {/* Bouton de déconnexion */}
        {user && (
          <div className="space-y-3">
            <h3 className="font-semibold">Actions</h3>
            <Button 
              onClick={handleTestLogout} 
              variant="outline"
              className="w-full"
            >
              Se Déconnecter
            </Button>
          </div>
        )}

        {/* Résultat du test */}
        {testResult && (
          <div className="space-y-3">
            <h3 className="font-semibold">Résultat du Test</h3>
            <div className="bg-muted p-4 rounded-lg">
              <code className="text-sm">{testResult}</code>
            </div>
          </div>
        )}

        {/* Comptes de test disponibles */}
        <div className="space-y-3">
          <h3 className="font-semibold">Comptes de Test Disponibles</h3>
          <div className="space-y-2 text-sm">
            <div className="bg-blue-50 p-3 rounded border">
              <strong>Utilisateur Test :</strong><br />
              Email: <EMAIL><br />
              Mot de passe: password123<br />
              Rôle: Loueur
            </div>
            <div className="bg-green-50 p-3 rounded border">
              <strong>Administrateur :</strong><br />
              Email: <EMAIL><br />
              Mot de passe: admin123<br />
              Rôle: Admin
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="space-y-3">
          <h3 className="font-semibold">Instructions</h3>
          <div className="text-sm text-muted-foreground space-y-1">
            <p>1. Utilisez les comptes de test ci-dessus pour tester l'authentification</p>
            <p>2. Vérifiez que les routes protégées redirigent vers /auth</p>
            <p>3. Testez la persistance de session en rafraîchissant la page</p>
            <p>4. Vérifiez que la déconnexion nettoie correctement la session</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AuthTester;
